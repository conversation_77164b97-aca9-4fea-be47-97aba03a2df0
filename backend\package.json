{"name": "cinepanda-backend", "version": "1.0.0", "description": "Node.js Express TypeScript server for CinePanda", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "dev:watch": "nodemon --exec ts-node src/server.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "echo 'Build completed successfully'", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["nodejs", "express", "typescript", "api", "server"], "author": "CinePanda Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}