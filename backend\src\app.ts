import express, { Application } from 'express';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

// Import middleware
import { morganMiddleware, requestIdMiddleware, requestLogger } from '@/middleware/logger';
import { globalErrorHandler, notFoundHandler } from '@/middleware/errorHandler';

// Import routes
import apiRoutes from '@/routes';

// Import configuration
import { env, isDevelopment } from '@/config/environment';
import { logger } from '@/utils/logger';

class App {
  public app: Application;

  constructor() {
    this.app = express();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // Trust proxy (important for rate limiting and IP detection)
    this.app.set('trust proxy', 1);

    // Request ID and timing middleware (should be first)
    this.app.use(requestIdMiddleware);

    // CORS configuration
    this.app.use(cors({
      origin: env.CORS_ORIGIN,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: env.RATE_LIMIT_WINDOW_MS,
      max: env.RATE_LIMIT_MAX_REQUESTS,
      message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
        timestamp: new Date().toISOString(),
      },
      standardHeaders: true,
      legacyHeaders: false,
      // Skip rate limiting for health checks
      skip: (req) => req.path === '/health' || req.path === '/health/ready',
    });
    this.app.use(limiter);

    // Compression middleware
    this.app.use(compression());

    // Body parsing middleware
    this.app.use(express.json({ 
      limit: '10mb',
      type: ['application/json', 'text/plain']
    }));
    this.app.use(express.urlencoded({ 
      extended: true, 
      limit: '10mb' 
    }));

    // Logging middleware
    this.app.use(morganMiddleware);
    if (isDevelopment) {
      this.app.use(requestLogger);
    }

    logger.info('Middlewares initialized successfully');
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use(env.API_PREFIX, apiRoutes);

    // Root endpoint
    this.app.get('/', (_req, res) => {
      res.json({
        success: true,
        message: 'CinePanda API Server',
        version: '1.0.0',
        environment: env.NODE_ENV,
        timestamp: new Date().toISOString(),
        documentation: `${env.API_PREFIX}/`,
      });
    });

    logger.info('Routes initialized successfully');
  }

  private initializeErrorHandling(): void {
    // 404 handler (must be after all routes)
    this.app.use(notFoundHandler);

    // Global error handler (must be last)
    this.app.use(globalErrorHandler);

    logger.info('Error handling initialized successfully');
  }

  public getApp(): Application {
    return this.app;
  }
}

export default App;
