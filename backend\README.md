# CinePanda Backend API

A robust Node.js Express TypeScript server boilerplate with production-ready features and best practices.

## 🚀 Features

- **TypeScript**: Full TypeScript support with strict type checking
- **Express.js**: Fast, unopinionated web framework
- **Security**: CORS, rate limiting, and input validation
- **Logging**: Structured logging with Morgan and custom logger
- **Error Handling**: Comprehensive error handling with custom error classes
- **Health Checks**: Built-in health and readiness endpoints
- **Hot Reload**: Development server with automatic restart
- **Testing**: Jest testing framework with supertest
- **Code Quality**: ESLint with TypeScript rules
- **Environment Config**: Flexible environment variable management
- **Request Tracking**: Unique request IDs for better debugging

## 📁 Project Structure

```
backend/
├── src/
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Custom middleware
│   ├── routes/            # API routes
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   ├── config/            # Configuration files
│   ├── __tests__/         # Test files
│   ├── app.ts             # Express app configuration
│   └── server.ts          # Server entry point
├── dist/                  # Compiled JavaScript output
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── jest.config.js         # Jest testing configuration
├── .eslintrc.js          # ESLint configuration
├── .env.example          # Environment variables template
└── README.md             # This file
```

## 🛠️ Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your configuration.

4. **Build the project**:
   ```bash
   npm run build
   ```

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm run dev
```
This starts the server with hot-reload using nodemon.

### Production Mode
```bash
npm run build
npm start
```

## 🧪 Testing

Run all tests:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

Run tests with coverage:
```bash
npm run test:coverage
```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with hot-reload |
| `npm run build` | Build the TypeScript project |
| `npm start` | Start production server |
| `npm test` | Run tests |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:coverage` | Run tests with coverage report |
| `npm run lint` | Run ESLint |
| `npm run lint:fix` | Run ESLint with auto-fix |
| `npm run clean` | Clean build directory |

## 🌐 API Endpoints

### Health Check
- `GET /api/v1/health` - Health check endpoint
- `GET /api/v1/health/ready` - Readiness check endpoint

### API Information
- `GET /` - Root API information
- `GET /api/v1/` - API version information

## ⚙️ Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` and modify as needed:

### Core Settings
- `NODE_ENV` - Environment (development/production/test)
- `PORT` - Server port (default: 3001)
- `API_PREFIX` - API route prefix (default: /api/v1)

### Security Settings
- `CORS_ORIGIN` - Allowed CORS origins
- `RATE_LIMIT_WINDOW_MS` - Rate limiting window
- `RATE_LIMIT_MAX_REQUESTS` - Max requests per window

### Logging
- `LOG_LEVEL` - Logging level (error/warn/info/debug)

## 🏗️ Architecture

### Middleware Stack
1. **Request ID & Timing** - Adds unique ID and timing to requests
2. **CORS** - Cross-origin resource sharing
3. **Rate Limiting** - Prevents abuse
4. **Compression** - Gzip compression
5. **Body Parsing** - JSON and URL-encoded parsing
6. **Logging** - Request/response logging

### Error Handling
- Custom `AppError` class for operational errors
- Global error handler with proper HTTP status codes
- Async error wrapper for route handlers
- 404 handler for unknown routes

### Logging
- Structured logging with timestamps
- Request ID tracking
- Different log levels for development/production
- HTTP request logging with Morgan

## 🔧 Development

### Adding New Routes
1. Create controller in `src/controllers/`
2. Create route file in `src/routes/`
3. Import and use in `src/routes/index.ts`

### Adding Middleware
1. Create middleware in `src/middleware/`
2. Import and use in `src/app.ts`

### Environment Variables
Add new variables to:
1. `.env.example` (with example values)
2. `src/config/environment.ts` (with validation)

## 🚀 Deployment

### Docker (Recommended)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3001
CMD ["node", "dist/server.js"]
```

### PM2
```bash
npm install -g pm2
npm run build
pm2 start dist/server.js --name "cinepanda-api"
```

## 📊 Monitoring

The application includes built-in monitoring endpoints:

- **Health Check**: `/api/v1/health` - Returns server health status
- **Readiness Check**: `/api/v1/health/ready` - Returns service readiness
- **Request Tracking**: All requests include unique IDs for tracing

## 🤝 Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation as needed
4. Run linting and tests before committing

## 📄 License

MIT License - see LICENSE file for details.
